package com.logictrue;

import com.logictrue.config.MyBatisPlusConfig;
import com.logictrue.entity.DeviceDetectionData;
import com.logictrue.entity.DeviceDetectionBasicField;
import com.logictrue.entity.DeviceDetectionTableHeader;
import com.logictrue.entity.DeviceDetectionTableData;
import com.logictrue.service.DeviceDetectionDataService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.AfterEach;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MyBatis-Plus功能测试类
 */
public class MyBatisPlusTest {
    private static final Logger logger = LoggerFactory.getLogger(MyBatisPlusTest.class);
    
    private DeviceDetectionDataService dataService;
    private MyBatisPlusConfig myBatisPlusConfig;

    @BeforeEach
    void setUp() {
        logger.info("初始化MyBatis-Plus测试环境");
        try {
            myBatisPlusConfig = MyBatisPlusConfig.getInstance();
            dataService = new DeviceDetectionDataService();
            logger.info("MyBatis-Plus测试环境初始化成功");
        } catch (Exception e) {
            logger.error("MyBatis-Plus测试环境初始化失败", e);
            fail("测试环境初始化失败: " + e.getMessage());
        }
    }

    @AfterEach
    void tearDown() {
        logger.info("清理MyBatis-Plus测试环境");
        if (myBatisPlusConfig != null) {
            myBatisPlusConfig.close();
        }
    }

    @Test
    void testInsertDeviceDetectionData() {
        logger.info("测试插入设备检测数据");
        
        // 创建测试数据
        DeviceDetectionData testData = new DeviceDetectionData();
        testData.setDeviceCode("TEST_DEVICE_001");
        testData.setTemplateCode("TEMPLATE_001");
        testData.setTemplateName("测试模板");
        testData.setFilePath("/test/path/test.xlsx");
        testData.setFileName("test.xlsx");
        testData.setFileSize(1024L);
        testData.setCreateBy("test_user");
        testData.setRemark("MyBatis-Plus测试数据");

        // 执行插入
        Long insertedId = dataService.insertBeforeParsing(testData);
        
        // 验证结果
        assertNotNull(insertedId, "插入应该返回有效的ID");
        assertTrue(insertedId > 0, "插入的ID应该大于0");
        
        logger.info("插入测试成功，ID: {}", insertedId);
        
        // 验证查询
        DeviceDetectionData retrievedData = dataService.getById(insertedId);
        assertNotNull(retrievedData, "应该能够查询到插入的数据");
        assertEquals(testData.getDeviceCode(), retrievedData.getDeviceCode(), "设备编码应该匹配");
        assertEquals(testData.getTemplateName(), retrievedData.getTemplateName(), "模板名称应该匹配");
        assertEquals(0, retrievedData.getParseStatus(), "初始解析状态应该为0");
        
        logger.info("查询验证测试成功");
    }

    @Test
    void testUpdateParseStatus() {
        logger.info("测试更新解析状态");
        
        // 先插入测试数据
        DeviceDetectionData testData = new DeviceDetectionData();
        testData.setDeviceCode("TEST_DEVICE_002");
        testData.setTemplateCode("TEMPLATE_002");
        testData.setTemplateName("状态更新测试模板");
        testData.setFilePath("/test/path/status_test.xlsx");
        testData.setFileName("status_test.xlsx");
        testData.setCreateBy("test_user");

        Long insertedId = dataService.insertBeforeParsing(testData);
        assertNotNull(insertedId, "插入应该成功");
        
        // 测试更新为进行中
        boolean updateResult = dataService.updateParsingInProgress(insertedId);
        assertTrue(updateResult, "更新解析状态为进行中应该成功");
        
        // 测试更新为成功
        updateResult = dataService.updateParsingSuccess(insertedId, "解析成功测试");
        assertTrue(updateResult, "更新解析状态为成功应该成功");
        
        // 验证更新结果
        DeviceDetectionData updatedData = dataService.getById(insertedId);
        assertNotNull(updatedData, "应该能够查询到更新后的数据");
        assertEquals(1, updatedData.getParseStatus(), "解析状态应该为1（成功）");
        assertEquals("解析成功测试", updatedData.getParseMessage(), "解析消息应该匹配");
        
        logger.info("状态更新测试成功");
    }

    @Test
    void testBatchInsertBasicFields() {
        logger.info("测试批量插入基础字段");
        
        // 先插入主数据
        DeviceDetectionData testData = new DeviceDetectionData();
        testData.setDeviceCode("TEST_DEVICE_003");
        testData.setTemplateCode("TEMPLATE_003");
        testData.setTemplateName("批量插入测试模板");
        testData.setFilePath("/test/path/batch_test.xlsx");
        testData.setFileName("batch_test.xlsx");
        testData.setCreateBy("test_user");

        Long insertedId = dataService.insertBeforeParsing(testData);
        assertNotNull(insertedId, "主数据插入应该成功");
        
        // 创建基础字段测试数据
        List<DeviceDetectionBasicField> basicFields = new ArrayList<>();
        
        for (int i = 1; i <= 3; i++) {
            DeviceDetectionBasicField field = new DeviceDetectionBasicField();
            field.setSheetId("SHEET_001");
            field.setSheetName("测试工作表");
            field.setFieldCode("FIELD_" + String.format("%03d", i));
            field.setFieldName("测试字段" + i);
            field.setFieldValue("测试值" + i);
            field.setFieldType("TEXT");
            field.setLabelRowIndex(i);
            field.setLabelColIndex(1);
            field.setValueRowIndex(i);
            field.setValueColIndex(2);
            basicFields.add(field);
        }
        
        // 执行批量插入
        boolean batchResult = dataService.batchInsertBasicFields(insertedId, basicFields);
        assertTrue(batchResult, "批量插入基础字段应该成功");
        
        logger.info("批量插入基础字段测试成功");
    }

    @Test
    void testBatchInsertTableHeaders() {
        logger.info("测试批量插入表头数据");
        
        // 先插入主数据
        DeviceDetectionData testData = new DeviceDetectionData();
        testData.setDeviceCode("TEST_DEVICE_004");
        testData.setTemplateCode("TEMPLATE_004");
        testData.setTemplateName("表头批量插入测试模板");
        testData.setFilePath("/test/path/header_test.xlsx");
        testData.setFileName("header_test.xlsx");
        testData.setCreateBy("test_user");

        Long insertedId = dataService.insertBeforeParsing(testData);
        assertNotNull(insertedId, "主数据插入应该成功");
        
        // 创建表头测试数据
        List<DeviceDetectionTableHeader> tableHeaders = new ArrayList<>();
        
        String[] headerNames = {"序号", "设备名称", "检测值", "状态"};
        for (int i = 0; i < headerNames.length; i++) {
            DeviceDetectionTableHeader header = new DeviceDetectionTableHeader();
            header.setSheetId("SHEET_001");
            header.setSheetName("测试工作表");
            header.setHeaderCode("HEADER_" + String.format("%03d", i + 1));
            header.setHeaderName(headerNames[i]);
            header.setHeaderPosition("A" + (i + 1));
            header.setHeaderRowIndex(1);
            header.setHeaderColIndex(i + 1);
            header.setDataType("TEXT");
            header.setColumnOrder(i + 1);
            tableHeaders.add(header);
        }
        
        // 执行批量插入
        boolean batchResult = dataService.batchInsertTableHeaders(insertedId, tableHeaders);
        assertTrue(batchResult, "批量插入表头数据应该成功");
        
        logger.info("批量插入表头数据测试成功");
    }

    @Test
    void testBatchInsertTableData() {
        logger.info("测试批量插入表格数据");
        
        // 先插入主数据
        DeviceDetectionData testData = new DeviceDetectionData();
        testData.setDeviceCode("TEST_DEVICE_005");
        testData.setTemplateCode("TEMPLATE_005");
        testData.setTemplateName("表格数据批量插入测试模板");
        testData.setFilePath("/test/path/table_data_test.xlsx");
        testData.setFileName("table_data_test.xlsx");
        testData.setCreateBy("test_user");

        Long insertedId = dataService.insertBeforeParsing(testData);
        assertNotNull(insertedId, "主数据插入应该成功");
        
        // 创建表格数据测试数据
        List<DeviceDetectionTableData> tableDataList = new ArrayList<>();
        
        for (int i = 1; i <= 5; i++) {
            DeviceDetectionTableData tableData = new DeviceDetectionTableData();
            tableData.setSheetId("SHEET_001");
            tableData.setSheetName("测试工作表");
            tableData.setRowIndex(i + 1); // 从第2行开始（第1行是表头）
            tableData.setRowData("{\"序号\":\"" + i + "\",\"设备名称\":\"设备" + i + "\",\"检测值\":\"" + (100 + i) + "\",\"状态\":\"正常\"}");
            tableData.setRowOrder(i);
            tableDataList.add(tableData);
        }
        
        // 执行批量插入
        boolean batchResult = dataService.batchInsertTableData(insertedId, tableDataList);
        assertTrue(batchResult, "批量插入表格数据应该成功");
        
        logger.info("批量插入表格数据测试成功");
    }

    @Test
    void testQueryMethods() {
        logger.info("测试查询方法");
        
        // 插入一些测试数据
        for (int i = 1; i <= 3; i++) {
            DeviceDetectionData testData = new DeviceDetectionData();
            testData.setDeviceCode("QUERY_TEST_" + String.format("%03d", i));
            testData.setTemplateCode("TEMPLATE_QUERY_" + i);
            testData.setTemplateName("查询测试模板" + i);
            testData.setFilePath("/test/path/query_test_" + i + ".xlsx");
            testData.setFileName("query_test_" + i + ".xlsx");
            testData.setCreateBy("test_user");
            
            Long insertedId = dataService.insertBeforeParsing(testData);
            assertNotNull(insertedId, "插入测试数据应该成功");
            
            // 更新部分数据的状态
            if (i % 2 == 0) {
                dataService.updateParsingSuccess(insertedId, "查询测试完成");
            }
        }
        
        // 测试分页查询
        List<DeviceDetectionData> pageData = dataService.getPageData(1, 10);
        assertNotNull(pageData, "分页查询应该返回结果");
        assertTrue(pageData.size() > 0, "分页查询应该有数据");
        
        // 测试按设备编码查询
        List<DeviceDetectionData> deviceData = dataService.getByDeviceCode("QUERY_TEST_001");
        assertNotNull(deviceData, "按设备编码查询应该返回结果");
        
        // 测试按解析状态查询
        List<DeviceDetectionData> statusData = dataService.getByParseStatus(0);
        assertNotNull(statusData, "按解析状态查询应该返回结果");
        
        // 测试统计总数
        long totalCount = dataService.getTotalCount();
        assertTrue(totalCount > 0, "总记录数应该大于0");
        
        logger.info("查询方法测试成功，总记录数: {}", totalCount);
    }
}
