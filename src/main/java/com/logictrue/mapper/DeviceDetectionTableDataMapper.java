package com.logictrue.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logictrue.iot.entity.DeviceDetectionTableData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 设备检测表格数据Mapper接口
 */
@Mapper
public interface DeviceDetectionTableDataMapper extends BaseMapper<DeviceDetectionTableData> {

    /**
     * 根据检测数据ID查询表格数据
     */
    @Select("SELECT * FROM device_detection_table_data " +
            "WHERE detection_data_id = #{detectionDataId} " +
            "ORDER BY sheet_id, row_order")
    List<DeviceDetectionTableData> selectByDetectionDataId(@Param("detectionDataId") Long detectionDataId);

    /**
     * 根据检测数据ID和工作表ID查询表格数据
     */
    @Select("SELECT * FROM device_detection_table_data " +
            "WHERE detection_data_id = #{detectionDataId} AND sheet_id = #{sheetId} " +
            "ORDER BY row_order")
    List<DeviceDetectionTableData> selectByDetectionDataIdAndSheetId(@Param("detectionDataId") Long detectionDataId,
                                                                     @Param("sheetId") String sheetId);

    /**
     * 根据检测数据ID统计表格数据行数
     */
    @Select("SELECT COUNT(*) FROM device_detection_table_data " +
            "WHERE detection_data_id = #{detectionDataId}")
    long countByDetectionDataId(@Param("detectionDataId") Long detectionDataId);

    /**
     * 根据检测数据ID删除表格数据
     */
    @Select("DELETE FROM device_detection_table_data " +
            "WHERE detection_data_id = #{detectionDataId}")
    int deleteByDetectionDataId(@Param("detectionDataId") Long detectionDataId);

    /**
     * 分页查询表格数据
     */
    @Select("SELECT * FROM device_detection_table_data " +
            "WHERE detection_data_id = #{detectionDataId} " +
            "ORDER BY sheet_id, row_order " +
            "LIMIT #{pageSize} OFFSET #{offset}")
    List<DeviceDetectionTableData> selectPageByDetectionDataId(@Param("detectionDataId") Long detectionDataId,
                                                               @Param("offset") int offset,
                                                               @Param("pageSize") int pageSize);
}
