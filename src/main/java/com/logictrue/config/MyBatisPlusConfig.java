package com.logictrue.config;

import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.toolkit.GlobalConfigUtils;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.logictrue.service.DatabaseService;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.io.File;

/**
 * MyBatis-Plus配置类
 */
@MapperScan("com.logictrue.mapper")
public class MyBatisPlusConfig {
    private static final Logger logger = LoggerFactory.getLogger(MyBatisPlusConfig.class);

    private static MyBatisPlusConfig instance;
    private SqlSessionFactory sqlSessionFactory;
    private SqlSessionTemplate sqlSessionTemplate;
    private DataSource dataSource;

    private MyBatisPlusConfig() {
        initializeMyBatisPlus();
    }

    /**
     * 获取单例实例
     */
    public static synchronized MyBatisPlusConfig getInstance() {
        if (instance == null) {
            instance = new MyBatisPlusConfig();
        }
        return instance;
    }

    /**
     * 初始化MyBatis-Plus
     */
    private void initializeMyBatisPlus() {
        try {
            // 创建数据源
            this.dataSource = createDataSource();
            
            // 创建SqlSessionFactory
            this.sqlSessionFactory = createSqlSessionFactory(dataSource);
            
            // 创建SqlSessionTemplate
            this.sqlSessionTemplate = new SqlSessionTemplate(sqlSessionFactory);
            
            logger.info("MyBatis-Plus初始化成功");
        } catch (Exception e) {
            logger.error("MyBatis-Plus初始化失败", e);
            throw new RuntimeException("MyBatis-Plus初始化失败", e);
        }
    }

    /**
     * 创建数据源
     */
    private DataSource createDataSource() {
        // 获取数据库路径
        String dbPath = DatabaseService.getInstance().getDbPath();
        String jdbcUrl = "jdbc:sqlite:" + dbPath;
        
        logger.info("配置SQLite数据源，路径: {}", dbPath);

        // 配置HikariCP连接池
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(jdbcUrl);
        config.setDriverClassName("org.sqlite.JDBC");
        
        // SQLite特定配置
        config.setMaximumPoolSize(1); // SQLite建议使用单连接
        config.setMinimumIdle(1);
        config.setConnectionTimeout(30000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
        
        // SQLite连接属性
        config.addDataSourceProperty("foreign_keys", "true");
        config.addDataSourceProperty("journal_mode", "WAL");
        config.addDataSourceProperty("synchronous", "NORMAL");
        
        return new HikariDataSource(config);
    }

    /**
     * 创建SqlSessionFactory
     */
    private SqlSessionFactory createSqlSessionFactory(DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean factoryBean = new MybatisSqlSessionFactoryBean();
        factoryBean.setDataSource(dataSource);
        
        // 配置MyBatis-Plus全局配置
        GlobalConfig globalConfig = new GlobalConfig();
        
        // 配置主键生成策略
        GlobalConfig.DbConfig dbConfig = new GlobalConfig.DbConfig();
        dbConfig.setIdType(com.baomidou.mybatisplus.annotation.IdType.AUTO);
        dbConfig.setTableUnderline(true); // 开启下划线转驼峰
        globalConfig.setDbConfig(dbConfig);
        
        factoryBean.setGlobalConfig(globalConfig);
        
        // 配置MyBatis设置
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true); // 开启驼峰命名转换
        configuration.setLogImpl(org.apache.ibatis.logging.slf4j.Slf4jImpl.class); // 配置日志
        factoryBean.setConfiguration(configuration);
        
        return factoryBean.getObject();
    }

    /**
     * 获取SqlSessionFactory
     */
    public SqlSessionFactory getSqlSessionFactory() {
        return sqlSessionFactory;
    }

    /**
     * 获取SqlSessionTemplate
     */
    public SqlSessionTemplate getSqlSessionTemplate() {
        return sqlSessionTemplate;
    }

    /**
     * 获取数据源
     */
    public DataSource getDataSource() {
        return dataSource;
    }

    /**
     * 关闭资源
     */
    public void close() {
        try {
            if (dataSource instanceof HikariDataSource) {
                ((HikariDataSource) dataSource).close();
                logger.info("数据源已关闭");
            }
        } catch (Exception e) {
            logger.error("关闭数据源失败", e);
        }
    }
}
